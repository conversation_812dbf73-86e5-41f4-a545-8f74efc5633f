describe('Add Liquidity', () => {
  it('loads the two correct tokens', () => {
    cy.visit('/add/******************************************-******************************************')
    cy.get('#add-liquidity-input-tokena .token-symbol-container').should('contain.text', 'MKR')
    cy.get('#add-liquidity-input-tokenb .token-symbol-container').should('contain.text', 'ETH')
  })

  it('does not crash if ETH is duplicated', () => {
    cy.visit('/add/******************************************-******************************************')
    cy.get('#add-liquidity-input-tokena .token-symbol-container').should('contain.text', 'ETH')
    cy.get('#add-liquidity-input-tokenb .token-symbol-container').should('not.contain.text', 'ETH')
  })

  it('token not in storage is loaded', () => {
    cy.visit('/add/******************************************-******************************************')
    cy.get('#add-liquidity-input-tokena .token-symbol-container').should('contain.text', 'SKL')
    cy.get('#add-liquidity-input-tokenb .token-symbol-container').should('contain.text', 'MKR')
  })

  it('single token can be selected', () => {
    cy.visit('/add/******************************************')
    cy.get('#add-liquidity-input-tokena .token-symbol-container').should('contain.text', 'SKL')
    cy.visit('/add/******************************************')
    cy.get('#add-liquidity-input-tokena .token-symbol-container').should('contain.text', 'MKR')
  })

  it('redirects /add/token-token to add/token/token', () => {
    cy.visit('/add/******************************************-******************************************')
    cy.url().should(
      'contain',
      '/add/******************************************/******************************************'
    )
  })

  it('redirects /add/WETH-token to /add/WETH-address/token', () => {
    cy.visit('/add/******************************************-******************************************')
    cy.url().should(
      'contain',
      '/add/******************************************/******************************************'
    )
  })

  it('redirects /add/token-WETH to /add/token/WETH-address', () => {
    cy.visit('/add/******************************************-******************************************')
    cy.url().should(
      'contain',
      '/add/******************************************/******************************************'
    )
  })
})
