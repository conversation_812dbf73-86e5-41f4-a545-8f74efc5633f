{"noWallet": "Кошелек эфира не найден", "wrongNetwork": "Некорректная сеть", "switchNetwork": "Пожалуйста, перейдите в {{ correctNetwork }}", "installWeb3MobileBrowser": "Пожалуйста, откройте в браузере с поддержкой web3, таком, как Trust Wallet или Coinbase Wallet.", "installMetamask": "Пожалуйста, откройте в Chrome или Brave с установленным расширением Metamask.", "disconnected": "Нет подключения", "swap": "Обменять", "send": "Отправить", "pool": "Вло<PERSON>ить", "betaWarning": "Проект находится на стадии бета тестирования.", "input": "Ввести", "output": "Вывести", "estimated": "по оценке", "balance": "Баланс: {{ balanceInput }}", "unlock": "Разблокировать", "pending": "Ожидание", "selectToken": "Выберите токен", "searchOrPaste": "Поиск токена или вставить адрес токена", "noExchange": "Обмен не найден", "exchangeRate": "Курс обмена", "enterValueCont": "Введите {{ missingCurrencyValue }}, чтобы продолжить.", "selectTokenCont": "Введите токен, чтобы продолжить.", "noLiquidity": "Нет ликвидности.", "unlockTokenCont": "Пожалуйста, разблокируйте токен, чтобы продолжить.", "transactionDetails": "Детали транзакции", "hideDetails": "Скрыть подробности", "youAreSelling": "Вы продаете", "orTransFail": "или транзакция будет отклонена.", "youWillReceive": "Вы получите как минимум", "youAreBuying": "Вы покупаете", "itWillCost": "В крайнем случае это будет стоить", "insufficientBalance": "Недостаточно средств", "inputNotValid": "Некорректное значение", "differentToken": "Должны быть разные токены.", "noRecipient": "Введите адрес кошелька эфира, куда перечислить.", "invalidRecipient": "Пожалуйста, введите корректный адрес кошелька получателя.", "recipientAddress": "Адрес получателя", "youAreSending": "Вы отправляете", "willReceive": "получит как минимум", "to": "", "addLiquidity": "Добавить ликвидность", "deposit": "Вло<PERSON>ить", "currentPoolSize": "Текущий размер пула", "yourPoolShare": "Ваша доля в пуле", "noZero": "Значение не может быть нулевым.", "mustBeETH": "Одно из значений должно быть ETH.", "enterCurrencyOrLabelCont": "Введите {{ inputCurrency }} или {{ label }}, чтобы продолжить.", "youAreAdding": "Вы добавляете от", "and": "и", "intoPool": "в пул ликвидности.", "outPool": "из пула ликвидности.", "youWillMint": "Вы произведёте", "liquidityTokens": "токенов ликвидности.", "totalSupplyIs": "Ваш объем токенов ликвидности", "youAreSettingExRate": "Вы устанавливаете начальный курс обмена", "totalSupplyIs0": "Ваш объем токенов ликвидности 0.", "tokenWorth": "При текущем курсе, каждый токен пула оценивается в", "firstLiquidity": "Вы первый, кто создаст ликвидность!", "initialExchangeRate": "Начальный курс обмена будет установлен согласно вашим депозитам. Убедитесь, что ваши депозиты ETH и {{ label }} имеют одинаковое значение в валюте.", "removeLiquidity": "Убрать ликвидность", "poolTokens": "Токены пула", "enterLabelCont": "Введите {{ label }}, чтобы продолжить.", "youAreRemoving": "Вы убираете в от", "youWillRemove": "Вы уберёте", "createExchange": "Создать обмен", "invalidTokenAddress": "Некорректный адрес токена", "exchangeExists": "{{ label }} Обмен уже существует!", "invalidSymbol": "Некорректный символ", "invalidDecimals": "Некорректное десятичное значение", "tokenAddress": "Адрес токена", "label": "Название", "decimals": "Десятичное значение", "enterTokenCont": "Чтобы продолжить, введите адрес токена"}