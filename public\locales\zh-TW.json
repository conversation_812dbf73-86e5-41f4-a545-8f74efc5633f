{"noWallet": "未偵測到以太坊錢包", "wrongNetwork": "你位在錯誤的網路", "switchNetwork": "請切換到 {{ correctNetwork }}", "installWeb3MobileBrowser": "請安裝含有 web3 瀏覽器的手機錢包，如 Trust Wallet 或 Coinbase Wallet。", "installMetamask": "請使用 Chrome 或 Brave 瀏覽器安裝 Metamask。", "disconnected": "未連接", "swap": "兌換", "send": "發送", "pool": "資金池", "betaWarning": "本產品仍在測試階段。使用者需自負風險。", "input": "輸入", "output": "輸出", "estimated": "估計", "balance": "餘額: {{ balanceInput }}", "unlock": "解鎖", "pending": "處理中", "selectToken": "選擇代幣", "searchOrPaste": "選擇代幣或輸入地址", "noExchange": "找不到交易所", "exchangeRate": "匯率", "enterValueCont": "輸入 {{ missingCurrencyValue }} 以繼續。", "selectTokenCont": "選擇代幣以繼續。", "noLiquidity": "沒有流動性資金。", "unlockTokenCont": "解鎖代幣以繼續。", "transactionDetails": "交易明細", "hideDetails": "隱藏明細", "youAreSelling": "你正在出售", "orTransFail": "或交易失敗。", "youWillReceive": "你將至少收到", "youAreBuying": "你正在購買", "itWillCost": "這將花費至多", "insufficientBalance": "餘額不足", "inputNotValid": "無效的輸入值", "differentToken": "必須是不同的代幣。", "noRecipient": "請輸入收款人錢包地址。", "invalidRecipient": "請輸入有效的錢包地址。", "recipientAddress": "收款人錢包地址", "youAreSending": "你正在發送", "willReceive": "將至少收到", "to": "至", "addLiquidity": "增加流動性資金", "deposit": "存入", "currentPoolSize": "目前的資金池總量", "yourPoolShare": "你在資金池中的佔比", "noZero": "金額不能為零。", "mustBeETH": "輸入中必須包含 ETH。", "enterCurrencyOrLabelCont": "輸入 {{ inputCurrency }} 或 {{ label }} 以繼續。", "youAreAdding": "你將把", "and": "和", "intoPool": "加入資金池。", "outPool": "領出資金池。", "youWillMint": "你將產生", "liquidityTokens": "流動性代幣。", "totalSupplyIs": "目前流動性代幣供給總量為", "youAreSettingExRate": "初始的匯率將被設定為", "totalSupplyIs0": "目前流動性代幣供給為零。", "tokenWorth": "依據目前的匯率，每個流動性代幣價值", "firstLiquidity": "您是第一個提供流動性資金的人！", "initialExchangeRate": "初始的匯率將取決於你存入的資金。請確保存入的 ETH 和 {{ label }} 的價值相等。", "removeLiquidity": "領出流動性資金", "poolTokens": "資金池代幣", "enterLabelCont": "輸入 {{ label }} 以繼續。", "youAreRemoving": "您正在移除", "youWillRemove": "您即將移除", "createExchange": "創建交易所", "invalidTokenAddress": "無效的代幣地址", "exchangeExists": "{{ label }} 的交易所已經存在！", "invalidSymbol": "代幣符號錯誤", "invalidDecimals": "小數位數錯誤", "tokenAddress": "代幣地址", "label": "代幣符號", "decimals": "小數位數", "enterTokenCont": "輸入代幣地址"}