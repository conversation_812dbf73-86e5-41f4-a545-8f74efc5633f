{"noWallet": "No se encontró billetera de Ethereum", "wrongNetwork": "Te encontrás en la red equivocada", "switchNetwork": "Por favor cambia a {{ correctNetwork }}", "installWeb3MobileBrowser": "Por favor ingresá desde un navegador móvil con web3 habilitado como Trust Wallet o Coinbase Wallet.", "installMetamask": "Por favor visítanos nuevamente luego de instalar Metamask en Chrome o Brave.", "disconnected": "Desconectado", "swap": "Intercambiar", "send": "Enviar", "pool": "Pool", "betaWarning": "Este proyecto se encuentra en beta. Usalo bajo tu propio riesgo.", "input": "Entrada", "output": "Salida", "estimated": "estimado", "balance": "Saldo: {{ balanceInput }}", "unlock": "Desb<PERSON>que<PERSON>", "pending": "Pendiente", "selectToken": "Seleccioná un token", "searchOrPaste": "Buscar Token o Pegar Dirección", "noExchange": "No se encontró la divisa", "exchangeRate": "<PERSON><PERSON>", "enterValueCont": "Ingresá un valor en {{ missingCurrencyValue }} para continuar.", "selectTokenCont": "Seleccioná un token para continuar.", "noLiquidity": "Sin liquidez.", "unlockTokenCont": "Por favor desbloqueá un token para continuar.", "transactionDetails": "Detalles de la transacción", "hideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON>", "youAreSelling": "Estás vendiendo", "orTransFail": "o la transacción fallará.", "youWillReceive": "Vas a recibir al menos", "youAreBuying": "Estás comprando", "itWillCost": "Costará a lo sumo", "insufficientBalance": "<PERSON><PERSON> insuficiente", "inputNotValid": "No es un valor de entrada válido", "differentToken": "Debe ser un token distinto.", "noRecipient": "Ingresá una dirección de billetera para enviar.", "invalidRecipient": "Por favor ingrese una billetera de destino válida.", "recipientAddress": "Dirección del recipiente", "youAreSending": "<PERSON><PERSON><PERSON>", "willReceive": "recibirá al menos", "to": "a", "addLiquidity": "Agregar liquidez", "deposit": "Depositar", "currentPoolSize": "Tamaño del Pool Actual", "yourPoolShare": "Tu parte del Pool", "noZero": "El monto no puede ser cero.", "mustBeETH": "Una de las entradas debe ser ETH.", "enterCurrencyOrLabelCont": "Ingresá un valor de {{ inputCurrency }} o de {{ label }} para continuar.", "youAreAdding": "Estás agregando entre", "and": "y", "intoPool": "en el pool de liquidez.", "outPool": "en el pool de liquidez.", "youWillMint": "Vas a acuñar", "liquidityTokens": "tokens de liquidez.", "totalSupplyIs": "El actual suministro total de tokens de liquidez es", "youAreSettingExRate": "Está configurando el tipo de cambio inicial a", "totalSupplyIs0": "El actual suministro total de tokens de liquidez es 0.", "tokenWorth": "Al tipo de cambio actual, cada token del pool vale", "firstLiquidity": "Sos la primer persona en agregar liquidez!", "initialExchangeRate": "El tipo de cambio inicial se establecerá en función de tus depósitos. Por favor, asegúrate de que tus depósitos en ETH y {{ label }} tengan el mismo valor fíat.", "removeLiquidity": "Remover Liquidez", "poolTokens": "Pool de Tokens", "enterLabelCont": "Ingresá un valor de {{ label }} para continuar.", "youAreRemoving": "<PERSON><PERSON><PERSON> quitando entre", "youWillRemove": "Vas a remover", "createExchange": "Crear divisa", "invalidTokenAddress": "No es una dirección de token válida", "exchangeExists": "La divisa {{ label }} ya existe!", "invalidSymbol": "Símbolo inválido", "invalidDecimals": "<PERSON><PERSON><PERSON>", "tokenAddress": "Dirección de Token", "label": "Etiqueta", "decimals": "<PERSON><PERSON><PERSON>", "enterTokenCont": "Ingresá una dirección de token para continuar"}