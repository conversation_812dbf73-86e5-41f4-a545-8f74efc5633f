{"noWallet": "Wallet Ethereum non trovato", "wrongNetwork": "Sei connesso alla rete sbagliata", "switchNetwork": "Perfavore connettiti su {{ correctNetwork }}", "installWeb3MobileBrowser": "Perfavore visita il sito da un browser abilitato web3 o da un app mobile come Trust Wallet o Coinbase Wallet.", "installMetamask": "Perfavore ritorna dopo aver installato Metamask su Chrome o Brave.", "disconnected": "Disconnesso", "swap": "Scambia", "swapAnyway": "Scambia comunque", "send": "Invia", "sendAnyway": "Invia comunque", "pool": "<PERSON><PERSON>", "betaWarning": "<PERSON>o progetto è in beta. Usalo a tuo rischio.", "input": "Input", "output": "Output", "estimated": "stimato", "balance": "Saldo: {{ balanceInput }}", "unlock": "S<PERSON><PERSON>ca", "pending": "In attesa", "selectToken": "Seleziona un token", "searchOrPaste": "Cerca Nome, Simbolo o Indirizzo Token", "searchOrPasteMobile": "Nome, Simbolo, o Indirizzo", "noExchange": "Nessun Exchange Trovato", "exchangeRate": "Tasso di cambio", "unknownError": "Oops! Si è verificato un Errore imprevisto. Aggiorna la pagina o visita da un altro browser o dispositivo.", "enterValueCont": "Inserisci un valore {{ missingCurrencyValue }} per continuare.", "selectTokenCont": "Seleziona un token per continuare.", "noLiquidity": "Nessuna liquidità.", "insufficientLiquidity": "Liquidità insufficiente.", "unlockTokenCont": "Si prega di sbloccare il token per continuare.", "transactionDetails": "<PERSON><PERSON><PERSON>", "hideDetails": "Nascondi dettagli", "slippageWarning": "Avviso di scostamento", "highSlippageWarning": "Avviso di elevato scostamento", "youAreSelling": "Stai vendendo", "orTransFail": "o la transazione fallità.", "youWillReceive": "Riceverai almeno", "youAreBuying": "Stai comprando", "itWillCost": "Costerà al massimo", "forAtMost": "per al massimo", "insufficientBalance": "<PERSON><PERSON> insufficente", "inputNotValid": "Non è un valore di input valido", "differentToken": "Deve essere un token diverso.", "noRecipient": "Inserisci un indirizzo di wallet a cui inviare.", "invalidRecipient": "Inserisci un destinatario valido per l'indirizzo del wallet.", "recipientAddress": "Indirizzo del destinatario", "youAreSending": "Stai inviando", "willReceive": "riceverà almeno", "to": "a", "addLiquidity": "Aggiungi liquidità", "deposit": "Depositare", "currentPoolSize": "Dimensione attuale del pool", "yourPoolShare": "La tua parte di pool condivisa", "noZero": "L'importo non può essere zero.", "mustBeETH": "Uno degli input deve essere ETH.", "enterCurrencyOrLabelCont": "Inserisci un valore {{ inputCurrency }} o {{ label }} per continuare.", "youAreAdding": "<PERSON><PERSON> a<PERSON>ugendo", "and": "e", "intoPool": "nella riserva di liquidità.", "outPool": "dalla riserva di liquidità.", "youWillMint": "<PERSON> conierai", "liquidityTokens": "token di liquidità.", "totalSupplyIs": "L'attuale disponibilità totale di token di liquidità è", "youAreSettingExRate": "Stai impostando il tasso di cambio iniziale su", "totalSupplyIs0": "L'attuale disponibilità totale di token di liquidità è 0.", "tokenWorth": "Al tasso di cambio corrente, ogni token del pool vale", "firstLiquidity": "Sei la prima persona ad aggiungere liquidità!", "initialExchangeRate": "Il tasso di cambio iniziale verrà impostato in base ai tuoi depositi. Assicurati che i tuoi depositi ETH e {{ label }} abb<PERSON> lo stesso valore fiat.", "removeLiquidity": "Rimuovi Liquidità", "poolTokens": "Token Pool", "enterLabelCont": "Inserisci un valore {{ label }} per continuare.", "youAreRemoving": "Stai rimuovendo tra", "youWillRemove": "Rimuoverai", "createExchange": "<PERSON><PERSON> scam<PERSON>", "invalidTokenAddress": "Indirizzo token non valido", "exchangeExists": "{{ label }} Exchange già esistente!", "invalidSymbol": "Simbolo non valido", "invalidDecimals": "Decimali non validi", "tokenAddress": "Indirizzo <PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "symbol": "Simbolo", "decimals": "<PERSON><PERSON><PERSON>", "enterTokenCont": "Inserire un indirizzo token per continuare", "priceChange": "Scostamento del prezzo previsto", "forAtLeast": "per almeno "}