import { Interface } from '@ethersproject/abi'
import { ChainId } from 'nodeswapv2-sdk'
import V1_EXCHANGE_ABI from './v1_exchange.json'
import V1_FACTORY_ABI from './v1_factory.json'

const V1_FACTORY_ADDRESSES: { [chainId in ChainId]: string } = {
  [ChainId.MAINNET]: '******************************************',
  [ChainId.ROPSTEN]: '******************************************',
  [ChainId.RINKEBY]: '******************************************',
  [ChainId.GÖRLI]: '******************************************',
  [ChainId.KOVAN]: '******************************************',
  [ChainId.BASESEPOLIA]: ''
}

const V1_FACTORY_INTERFACE = new Interface(V1_FACTORY_ABI)
const V1_EXCHANGE_INTERFACE = new Interface(V1_EXCHANGE_ABI)

export { V1_FACTORY_ADDRESSES, V1_FACTORY_INTERFACE, V1_FACTORY_ABI, V1_EXCHANGE_INTERFACE, V1_EXCHANGE_ABI }
  