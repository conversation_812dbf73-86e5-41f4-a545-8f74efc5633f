import { ChainId, Currency, CurrencyAmount, ETHER, Token, TokenAmount, WETH } from 'nodeswapv2-sdk'

export function wrappedCurrency(currency: Currency | undefined, chainId: ChainId | undefined): Token | undefined {
  if (!chainId || currency !== ETHER) {
    return currency instanceof Token ? currency : undefined
  }
  
  try {
    return WETH[chainId]
  } catch (e) {
    return undefined
  }
}

export function wrappedCurrencyAmount(
  currencyAmount: CurrencyAmount | undefined,
  chainId: ChainId | undefined
): TokenAmount | undefined {
  const token = currencyAmount && chainId ? wrappedCurrency(currencyAmount.currency, chainId) : undefined
  return token && currencyAmount ? new TokenAmount(token, currencyAmount.raw) : undefined
}

export function unwrappedToken(token: Token): Currency {
  try {
    if (WETH[token.chainId] && token.equals(WETH[token.chainId])) return ETHER
  } catch (e) {
    // If WETH access fails, just return the token
  }
  return token
}
