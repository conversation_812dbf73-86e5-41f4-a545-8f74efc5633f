{"noWallet": "No Ethereum wallet found", "wrongNetwork": "You are on the wrong network", "switchNetwork": "Please switch to {{ correctNetwork }}", "installWeb3MobileBrowser": "Please visit us from a web3-enabled mobile browser such as Trust Wallet or Coinbase Wallet.", "installMetamask": "Please visit us after installing Metamask on Chrome or Brave.", "disconnected": "Disconnected", "swap": "<PERSON><PERSON><PERSON>", "swapAnyway": "<PERSON>wap <PERSON>", "send": "Send", "sendAnyway": "Send Anyway", "pool": "Pool", "betaWarning": "This project is in beta. Use at your own risk.", "input": "Input", "output": "Output", "estimated": "estimated", "balance": "Balance: {{ balanceInput }}", "unlock": "Unlock", "pending": "Pending", "selectToken": "Select a token", "searchOrPaste": "Search Token Name, Symbol, or Address", "searchOrPasteMobile": "Name, Symbol, or Address", "noExchange": "No Exchange Found", "noToken": "No Token Found", "exchangeRate": "Exchange Rate", "unknownError": "Oops! An unknown error occurred. Please refresh the page, or visit from another browser or device.", "enterValueCont": "Enter a {{ missingCurrencyValue }} value to continue.", "selectTokenCont": "Select a token to continue.", "noLiquidity": "No liquidity.", "insufficientLiquidity": "Insufficient liquidity.", "unlockTokenCont": "Please unlock token to continue.", "transactionDetails": "Advanced Details", "hideDetails": "Hide Details", "slippageWarning": "Slippage Warning", "highSlippageWarning": "High Slippage Warning", "youAreSelling": "You are selling", "orTransFail": "or the transaction will fail.", "youWillReceive": "You will receive at least", "youAreBuying": "You are buying", "itWillCost": "It will cost at most", "forAtMost": "for at most", "insufficientBalance": "Insufficient Balance", "inputNotValid": "Not a valid input value", "differentToken": "Must be different token.", "noRecipient": "Enter a wallet address to send to.", "invalidRecipient": "Please enter a valid wallet address recipient.", "recipientAddress": "Recipient Address", "youAreSending": "You are sending", "willReceive": "will receive at least", "to": "to", "addLiquidity": "Add Liquidity", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "currentPoolSize": "Current Pool Size", "yourPoolShare": "Your Pool Share", "noZero": "Amount cannot be zero.", "mustBeETH": "One of the input must be ETH.", "enterCurrencyOrLabelCont": "Enter a {{ inputCurrency }} or {{ label }} value to continue.", "youAreAdding": "You are adding", "and": "and", "intoPool": "into the liquidity pool.", "outPool": "from the liquidity pool.", "youWillMint": "You will mint", "liquidityTokens": "liquidity tokens.", "totalSupplyIs": "Current total supply of liquidity tokens is", "youAreSettingExRate": "You are setting the initial exchange rate to", "totalSupplyIs0": "Current total supply of liquidity tokens is 0.", "tokenWorth": "At current exchange rate, each pool token is worth", "firstLiquidity": "You are the first person to add liquidity!", "initialExchangeRate": "The initial exchange rate will be set based on your deposits. Please make sure that your ETH and {{ label }} deposits have the same fiat value.", "removeLiquidity": "Remove Liquidity", "poolTokens": "Pool Tokens", "enterLabelCont": "Enter a {{ label }} value to continue.", "youAreRemoving": "You are removing between", "youWillRemove": "You will remove", "createExchange": "Create Exchange", "invalidTokenAddress": "Not a valid token address", "exchangeExists": "{{ label }} Exchange already exists!", "invalidSymbol": "Invalid symbol", "invalidDecimals": "Invalid decimals", "tokenAddress": "Token Address", "label": "Label", "name": "Name", "symbol": "Symbol", "decimals": "Decimals", "enterTokenCont": "Enter a token address to continue", "priceChange": "Expected price slippage", "forAtLeast": "for at least ", "brokenToken": "The selected token is not compatible with Uniswap V1. Adding liquidity will result in locked funds.", "toleranceExplanation": "Lowering this limit decreases your risk of frontrunning. However, this makes more likely that your transaction will fail due to normal price movements.", "tokenSearchPlaceholder": "Search name or paste address"}