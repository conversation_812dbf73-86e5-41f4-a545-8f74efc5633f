{"noWallet": "No se ha encontrado billetera de Ethereum", "wrongNetwork": "Se encuentra en la red equivocada", "switchNetwork": "Por favor cambie a {{ correctNetwork }}", "installWeb3MobileBrowser": "Por favor ingrese desde un navegador móvil con web3 habilitado como Trust Wallet o Coinbase Wallet.", "installMetamask": "Por favor visítenos nuevamente luego de instalar Metamask en Chrome o Brave.", "disconnected": "Desconectado", "swap": "Intercambiar", "send": "Enviar", "pool": "Pool", "betaWarning": "Este proyecto se encuentra en beta. Úselo bajo tu propio riesgo.", "input": "Entrada", "output": "Salida", "estimated": "estimado", "balance": "Saldo: {{ balanceInput }}", "unlock": "Desb<PERSON>que<PERSON>", "pending": "Pendiente", "selectToken": "Seleccione un token", "searchOrPaste": "Buscar Token o Pegar Dirección", "noExchange": "No se ha encontrado la divisa", "exchangeRate": "<PERSON><PERSON>", "enterValueCont": "Ingrese un valor en {{ missingCurrencyValue }} para continuar.", "selectTokenCont": "Seleccione un token para continuar.", "noLiquidity": "Sin liquidez.", "unlockTokenCont": "Por favor desbloquea un token para continuar.", "transactionDetails": "Detalles de la transacción", "hideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON>", "youAreSelling": "Está vendiendo", "orTransFail": "o la transacción fallará.", "youWillReceive": "Va a recibir al menos", "youAreBuying": "Está comprando", "itWillCost": "Costará a lo sumo", "insufficientBalance": "<PERSON><PERSON> insuficiente", "inputNotValid": "No es un valor de entrada válido", "differentToken": "Debe ser un token distinto.", "noRecipient": "Ingrese una dirección de billetera para enviar.", "invalidRecipient": "Por favor ingrese una billetera de destino válida.", "recipientAddress": "Dirección del recipiente", "youAreSending": "Est<PERSON>", "willReceive": "recibirá al menos", "to": "a", "addLiquidity": "Agregar liquidez", "deposit": "Depositar", "currentPoolSize": "Tamaño del Pool Actual", "yourPoolShare": "Su parte del Pool", "noZero": "El monto no puede ser cero.", "mustBeETH": "Una de las entradas debe ser ETH.", "enterCurrencyOrLabelCont": "Ingrese un valor de {{ inputCurrency }} o de {{ label }} para continuar.", "youAreAdding": "Está agregando entre", "and": "y", "intoPool": "en el pool de liquidez.", "outPool": "en el pool de liquidez.", "youWillMint": "Va a acuñar", "liquidityTokens": "tokens de liquidez.", "totalSupplyIs": "El actual suministro total de tokens de liquidez es", "youAreSettingExRate": "Está configurando el tipo de cambio inicial a", "totalSupplyIs0": "El actual suministro total de tokens de liquidez es 0.", "tokenWorth": "Al tipo de cambio actual, cada token del pool vale", "firstLiquidity": "Es la primer persona en agregar liquidez!", "initialExchangeRate": "El tipo de cambio inicial se establecerá en función de sus depósitos. Por favor, asegúrese de que sus depósitos en ETH y {{ label }} tengan el mismo valor fíat.", "removeLiquidity": "Remover Liquidez", "poolTokens": "Pool de Tokens", "enterLabelCont": "Ingresa un valor de {{ label }} para continuar.", "youAreRemoving": "<PERSON>st<PERSON> quitando entre", "youWillRemove": "Va a remover", "createExchange": "Crear tipo de cambio", "invalidTokenAddress": "No es una dirección de token válida", "exchangeExists": "El tipo de cambio {{ label }} ya existe!", "invalidSymbol": "Símbolo inválido", "invalidDecimals": "<PERSON><PERSON><PERSON>", "tokenAddress": "Dirección de Token", "label": "Etiqueta", "decimals": "<PERSON><PERSON><PERSON>", "enterTokenCont": "Ingrese una dirección de token para continuar"}