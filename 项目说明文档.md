# SepoSwap 项目说明文档

## 项目概述

**SepoSwap** 是一个基于 Base Sepolia 测试网的去中心化交易所（DEX）前端应用。作为通往 Base Sepolia DeFi 生态系统的门户，SepoSwap 为用户提供了无缝的代币交换、智能流动性管理和直观的用户界面。

### 项目定位

- **目标网络**：Base Sepolia 测试网
- **协议基础**：Uniswap V2 协议
- **核心功能**：代币交换、流动性管理、资金池管理

## 技术架构

### 前端技术栈

```
核心技术：
├── React 16.13.1 + TypeScript 3.8.3
├── Web3-React (钱包连接)
├── Ethers.js 5.0.7 (区块链交互)
├── Redux Toolkit (状态管理)
├── Styled-components (样式管理)
└── Cypress 4.11.0 (端到端测试)
```

### 区块链配置

- **链 ID**：84532 (Base Sepolia)
- **RPC 节点**：`https://base-sepolia.g.alchemy.com/v2/ml-gstsnT9C45zdylFMVQ`
- **路由合约**：`******************************************`
- **网络类型**：测试网

## 核心功能详解

### 1. 代币交换功能

#### 主要特性：

- **自动路由**：智能寻找最优交换路径
- **价格影响实时显示**：可视化显示交易对价格的影响
- **滑点保护**：可配置的滑点容忍度（默认 0.5%）
- **多跳路由**：支持通过多个交易对完成交换

#### 技术实现：

- 使用 `nodeswapv2-sdk` 进行路由计算
- 实时获取链上流动性数据
- 支持 ERC-20 代币之间的任意交换

### 2. 流动性管理

#### 添加流动性：

- 支持任意 ERC-20 代币对
- 自动计算所需的代币比例
- 实时显示池份额和预估收益

#### 移除流动性：

- 按比例提取代币
- 显示无常损失指标
- 支持部分或全部移除

### 3. 资金池管理

- **资金池浏览**：查看所有可用的交易对
- **个人仓位**：管理用户的流动性仓位
- **收益追踪**：显示手续费收益情况

### 4. 钱包集成

#### 支持的钱包：

1. **MetaMask** - 浏览器扩展钱包
2. **WalletConnect** - 移动端钱包连接
3. **Coinbase Wallet** - Coinbase 官方钱包
4. **Fortmatic** - 托管式钱包
5. **Portis** - 托管式钱包

#### 连接特性：

- 自动网络检测和切换提醒
- 多钱包支持，可自由切换
- 交易状态实时追踪
- Gas 费预估和显示

## 项目结构详解

```
SepoSwap/
├── src/                          # 源代码目录
│   ├── components/               # 可复用UI组件
│   │   ├── AccountDetails/      # 账户详情组件
│   │   ├── CurrencyInputPanel/  # 代币输入面板
│   │   ├── Header/              # 页面头部组件
│   │   ├── Modal/               # 模态框组件
│   │   ├── swap/                # 交换相关组件
│   │   └── Web3Status/          # Web3状态组件
│   ├──
│   ├── connectors/              # Web3连接器
│   │   ├── Fortmatic.ts         # Fortmatic连接器
│   │   ├── index.ts             # 连接器导出
│   │   └── NetworkConnector.ts  #网络连接器
│   ├── constants/               # 应用常量
│   │   ├── abis/               # 合约ABI文件
│   │   ├── index.ts            # 主要常量定义
│   │   └── lists.ts            # 代币列表配置
│   ├── hooks/                   # 自定义React Hooks
│   │   ├── useSwapCallback.ts  # 交换回调Hook
│   │   ├── useContract.ts      # 合约交互Hook
│   │   ├── useUSDCPrice.ts     # USD价格Hook
│   │   └── Web3ReactManager.tsx # Web3状态管理
│   ├── pages/                   # 页面组件
│   │   ├── App.tsx             # 主应用组件
│   │   ├── Swap/               # 交换页面
│   │   ├── Pool/               # 资金池页面
│   │   ├── AddLiquidity/       # 添加流动性页面
│   │   └── RemoveLiquidity/    # 移除流动性页面
│   ├── state/                   # Redux状态管理
│   │   ├── application/        # 应用状态
│   │   ├── swap/               # 交换状态
│   │   ├── transactions/       # 交易状态
│   │   └── wallet/             # 钱包状态
│   ├── theme/                   # 主题配置
│   │   ├── index.tsx           # 主题定义
│   │   └── components.tsx      # 主题组件
│   └── utils/                   # 工具函数
│       ├── index.ts            # 主要工具函数
│       ├── prices.ts           # 价格计算
│       └── useUSDCPrice.ts     # USD价格计算
├── public/                      # 静态资源
│   ├── locales/                # 国际化文件
│   │   ├── zh-CN.json          # 简体中文
│   │   ├── en.json             # 英文
│   │   └── ...                 # 其他语言
│   └── images/                 # 图片资源
├── cypress/                     # E2E测试
│   ├── integration/            # 集成测试
│   │   ├── swap.test.ts        # 交换测试
│   │   ├── pool.test.ts        # 资金池测试
│   │   └── add-liquidity.test.ts # 添加流动性测试
│   └── support/                # 测试支持文件
├── .env                        # 环境变量配置
├── package.json                # 项目依赖和脚本
└── tsconfig.json               # TypeScript配置
```

## 开发环境搭建

### 前置要求

```bash
# Node.js版本要求
node --version  # 需要 v14 或更高版本

# Yarn包管理器
yarn --version  # 建议使用最新版本
```

### 安装步骤

```bash
# 1. 克隆项目仓库
git clone <repository-url>
cd SepoSwap

# 2. 安装依赖
yarn install

# 3. 配置环境变量
cp .env.example .env  # 如果没有则创建
# 编辑 .env 文件，填入必要的配置

# 4. 启动开发服务器
yarn start
```

### 环境变量配置

```bash
# 必需的环境变量
REACT_APP_CHAIN_ID="84532"
REACT_APP_NETWORK_URL="https://base-sepolia.g.alchemy.com/v2/ml-gstsnT9C45zdylFMVQ"

# 可选的环境变量
REACT_APP_PORTIS_ID="your-portis-id"
REACT_APP_FORTMATIC_KEY="your-fortmatic-key"
REACT_APP_GOOGLE_ANALYTICS_ID="your-ga-id"
```

## 主要开发命令

### 开发阶段

```bash
yarn start              # 启动开发服务器 (http://localhost:3000)
yarn test               # 运行单元测试
yarn lint               # 运行代码检查
```

### 构建和部署

```bash
yarn build              # 构建生产版本
yarn integration-test   # 运行端到端测试
yarn deploy             # 部署到GitHub Pages
```

### 注意事项

- 开发服务器使用 `--openssl-legacy-provider` 选项以确保兼容性
- 使用 Yarn 而不是 npm 以避免依赖冲突
- 构建过程会自动处理 TypeScript 编译和代码优化

## 核心功能实现原理

### 代币交换流程

1. **用户输入**：用户选择输入/输出代币和数量
2. **路由计算**：使用 nodeswapv2-sdk 计算最优交换路径
3. **价格影响**：实时计算并显示价格滑点
4. **交易确认**：用户确认交易详情
5. **链上执行**：通过 Web3 提供商发送交易
6. **状态更新**：监听交易状态并更新 UI

### 流动性管理流程

1. **代币选择**：用户选择要提供流动性的代币对
2. **数量计算**：根据当前池比例计算所需代币数量
3. **授权检查**：检查代币授权状态
4. **流动性添加**：执行添加流动性交易
5. **LP 代币接收**：接收流动性提供者代币

### 数据获取机制

- **链上数据**：通过 Ethers.js 调用合约方法
- **代币价格**：使用 USDC 作为基准计算相对价格
- **交易历史**：监听区块链事件获取历史记录
- **用户余额**：实时查询钱包代币余额

## 国际化支持

### 支持的语言

- 简体中文 (zh-CN)
- 繁体中文 (zh-TW)
- 英语 (en)
- 德语 (de)
- 西班牙语 - 阿根廷 (es-AR)
- 西班牙语 - 美国 (es-US)
- 意大利语 (it-IT)
- 俄语 (ru)
- 越南语 (vi)
- 罗马尼亚语 (ro)
- 希伯来语 (iw)

### 语言文件位置

所有翻译文件位于 `public/locales/` 目录下，采用 JSON 格式存储。

## 测试策略

### 单元测试

- 使用 Jest 测试框架
- 测试工具函数和 Hook
- 运行命令：`yarn test`

### 端到端测试

- 使用 Cypress 测试框架
- 覆盖主要用户流程：
  - 代币交换流程
  - 添加/移除流动性
  - 钱包连接功能
  - 多语言切换
- 运行命令：`yarn integration-test`

### 测试数据

- 使用 Base Sepolia 测试网
- 需要测试网 ETH 进行交互
- 测试代币可通过水龙头获取

## 常见问题解决

### 开发环境问题

1. **Node.js 版本错误**

   ```bash
   # 检查版本
   node --version
   # 建议使用 nvm 管理 Node.js 版本
   ```

2. **OpenSSL 错误**

   - 项目已配置 `--openssl-legacy-provider` 选项
   - 如仍有问题，尝试更新 Node.js 版本

3. **依赖安装失败**
   ```bash
   # 清除缓存后重新安装
   yarn cache clean
   rm -rf node_modules
   yarn install
   ```

### 运行时问题

1. **钱包连接失败**

   - 确保钱包已切换到 Base Sepolia 网络
   - 检查网络配置是否正确
   - 刷新页面重试

2. **交易失败**

   - 检查是否有足够的测试网 ETH
   - 确认代币授权已完成
   - 调整滑点容忍度

3. **数据加载缓慢**
   - 检查 RPC 节点状态
   - 考虑更换 RPC 提供商
   - 清理浏览器缓存

## 性能优化建议

### 代码层面

1. **组件优化**

   - 使用 React.memo 减少不必要的重渲染
   - 合理使用 useMemo 和 useCallback
   - 实现虚拟滚动处理长列表

2. **状态管理**

   - 合理拆分 Redux store
   - 使用 selector 减少不必要的状态更新
   - 实现状态的本地持久化

3. **数据获取**
   - 实现数据的缓存机制
   - 使用分页处理大量数据
   - 优化 Web3 调用频率

### 用户体验

1. **加载优化**

   - 实现骨架屏提升感知性能
   - 使用懒加载减少初始加载时间
   - 优化图片和静态资源

2. **交互优化**
   - 提供清晰的操作反馈
   - 实现交易状态的实时更新
   - 优化移动端体验

## 安全注意事项

### 开发安全

1. **密钥管理**

   - 永远不要提交私钥到代码库
   - 使用环境变量存储敏感信息
   - 定期更新依赖包修复安全漏洞

2. **代码安全**
   - 验证所有用户输入
   - 防范 XSS 攻击
   - 使用 CSP 策略

### 用户安全

1. **交易安全**

   - 明确显示交易详情
   - 提供交易确认步骤
   - 实现滑点保护机制

2. **钱包安全**
   - 支持硬件钱包连接
   - 提供断开连接功能
   - 不存储用户私钥

## 部署和维护

### 构建部署

```bash
# 1. 构建生产版本
yarn build

# 2. 运行集成测试
yarn integration-test

# 3. 部署到 GitHub Pages
yarn deploy
```

### 监控维护

1. **性能监控**

   - 监控页面加载时间
   - 跟踪交易成功率
   - 监控错误率

2. **用户反馈**
   - 收集用户反馈
   - 定期更新功能
   - 修复已知问题

## 扩展功能建议

### 短期扩展

1. **价格图表**：集成 TradingView 图表
2. **交易历史**：显示详细的交易历史
3. **收益农场**：集成流动性挖矿功能
4. **限价订单**：实现限价交易功能

### 长期规划

1. **跨链支持**：支持多个区块链网络
2. **Layer 2 集成**：集成 Layer 2 解决方案
3. **NFT 功能**：支持 NFT 交易
4. **治理功能**：实现社区治理机制

---

本文档为 SepoSwap 项目提供了全面的技术说明和开发指南，帮助开发者快速理解和参与项目开发。随着项目的发展，文档内容将持续更新和完善。
