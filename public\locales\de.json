{"noWallet": "Keine Ethereum-Wallet gefunden", "wrongNetwork": "Du bist auf dem falschen Netzwerk.", "switchNetwork": "Bitte wechsle zum {{ correctNetwork }}", "installWeb3MobileBrowser": "Bitte besuche uns mit einem web3-fähigen mobilen Browser wie z.B. Trust Wallet oder Coinbase Wallet.", "installMetamask": "<PERSON>te besuch uns erneut, nachdem du Metamask oder Brave installiert hast.", "disconnected": "Nicht verbunden", "swap": "Tauschen", "swapAnyway": "Trotzdem tauschen", "send": "Senden", "sendAnyway": "Trotzdem senden", "pool": "Pool", "betaWarning": "Dieses Projekt ist in beta. Nutzung auf eigenes Risiko.", "input": "Input", "output": "Output", "estimated": "geschätzt", "balance": "Guthaben: {{ balanceInput }}", "unlock": "Freischalten", "pending": "häng<PERSON>", "selectToken": "Token auswählen", "searchOrPaste": "Token Name, Symbol oder Adresse suchen", "searchOrPasteMobile": "Name, Symbol oder Adresse", "noExchange": "Exchange nicht gefunden", "exchangeRate": "Wechselkurs", "invertedRate": "Invertierter Wechselkurs", "unknownError": "Oops! Ein unbekannter Fehler ist aufgetreten. Bitte Seite neu laden oder uns von einem anderen Browser oder Gerät erneut besuchen.", "enterValueCont": "Wert {{ missing<PERSON><PERSON><PERSON>cyV<PERSON>ue }} eingeben um fortzufahren.", "selectTokenCont": "Token auswählen um fortzufahren.", "noLiquidity": "<PERSON>ine Liquidität.", "insufficientLiquidity": "Liquidität ungenügend.", "unlockTokenCont": "Token freischalten um fortzufahren.", "transactionDetails": "Details der Transaktion", "hideDetails": "Details ausblenden", "slippageWarning": "Wechselkursrutsch", "highSlippageWarning": "<PERSON><PERSON> Wechselkursru<PERSON>ch", "youAreSelling": "<PERSON>", "orTransFail": "oder die Transaktion wird fehlschlagen.", "youWillReceive": "Du erhältst mindestens", "youAreBuying": "<PERSON>", "itWillCost": "<PERSON>s kostet höchstens", "forAtMost": "für maximal", "insufficientBalance": "<PERSON><PERSON><PERSON><PERSON> ungenügend", "inputNotValid": "Eingabewert ungültig", "differentToken": "<PERSON><PERSON> müssen unterschiedliche Token sein.", "noRecipient": "Empfängeradresse angeben.", "invalidRecipient": "Bitte gib eine gültige Empfängeradresse an.", "recipientAddress": "Adresse des Empfängers", "youAreSending": "<PERSON>", "willReceive": "<PERSON>h<PERSON><PERSON> mindestens", "to": "zu", "addLiquidity": "Liquidität hinzufügen", "deposit": "Depot", "currentPoolSize": "Aktuelle Größe des Pools", "yourPoolShare": "<PERSON><PERSON> am <PERSON>", "noZero": "<PERSON>rt darf nicht <PERSON>ull sein.", "mustBeETH": "Einer der Inputs muß ETH sein.", "enterCurrencyOrLabelCont": "{{ inputCurrency }} oder {{ label }} Wert eingeben um fortzufahren.", "youAreAdding": "Du fügst zwischen", "and": "und", "intoPool": "in den Liquiditätspool.", "outPool": "vom Liquiditätspool.", "youWillMint": "<PERSON>ägst", "liquidityTokens": "Liquiditätstokens.", "totalSupplyIs": "Die gesamte Anzahl Liquiditätstokens ist aktuell", "youAreSettingExRate": "Du setzt den anfänglichen Wechselkurs auf", "totalSupplyIs0": "Die gesamte Anzahl Liquiditätstokens ist aktuell 0.", "tokenWorth": "Zum gegenwärtigen Wechselkurs ist jeder Pool Token so viel Wert", "firstLiquidity": "Du bist die erste Person die Liquidität bereitstellt!", "initialExchangeRate": "Der initiale Wechselkurs wird auf deiner Überweisung basieren. <PERSON><PERSON> sic<PERSON>, dass deine ETH und {{ label }} denselben Fiatwert haben.", "removeLiquidity": "Liquidität entfernen", "poolTokens": "Pool Tokens", "enterLabelCont": "{{ label }} Wert eingeben um fortzufahren.", "youAreRemoving": "Du entfernst zwischen", "youWillRemove": "<PERSON>", "createExchange": "Exchange erstellen", "invalidTokenAddress": "Ungültige Tokenadresse", "exchangeExists": "{{ label }} Exchange existiert bereits!", "invalidSymbol": "Symbol ungültig", "invalidDecimals": "Dezimalstellen ungültig", "tokenAddress": "Tokenadresse", "label": "Label", "name": "Name", "symbol": "Symbol", "decimals": "Dezimalstellen", "enterTokenCont": "Tokenadresse eingeben um fortzufahren", "priceChange": "Geschätzter Wechselkursrutsch", "forAtLeast": "für mindestens ", "brokenToken": "Der ausgewählte Token ist nicht kompatibel mit Uniswap V1. Liquidität hinzufügen wird zu nicht mehr zugänglichen Token führen!"}