{"noWallet": "未发现以太钱包", "wrongNetwork": "网络错误", "switchNetwork": "请切换到 {{ correctNetwork }}", "installWeb3MobileBrowser": "请从支持web3的移动端浏览器，如 Trust Wallet 或 Coinbase Wallet 访问。", "installMetamask": "请从安装了 Metamask 插件的 Chrome 或 Brave 访问。", "disconnected": "未连接", "swap": "兑换", "send": "发送", "pool": "资金池", "betaWarning": "项目尚处于beta阶段。使用需自行承担风险。", "input": "输入", "output": "输出", "estimated": "估计", "balance": "余额: {{ balanceInput }}", "unlock": "解锁", "pending": "处理中", "selectToken": "选择通证", "searchOrPaste": "搜索通证或粘贴地址", "noExchange": "未找到交易所", "exchangeRate": "兑换率", "enterValueCont": "输入{{ missingCurrencyValue }}值并继续。", "selectTokenCont": "选取通证继续。", "noLiquidity": "没有流动金。", "unlockTokenCont": "请解锁通证并继续。", "transactionDetails": "交易明细", "hideDetails": "隐藏明细", "youAreSelling": "你正在出售", "orTransFail": "或交易失败。", "youWillReceive": "你将至少收到", "youAreBuying": "你正在购买", "itWillCost": "它将至少花费", "insufficientBalance": "余额不足", "inputNotValid": "无效的输入值", "differentToken": "必须是不同的通证。", "noRecipient": "输入接收钱包地址。", "invalidRecipient": "请输入有效的收钱地址。", "recipientAddress": "接收地址", "youAreSending": "你正在发送", "willReceive": "将至少收到", "to": "至", "addLiquidity": "添加流动金", "deposit": "存入", "currentPoolSize": "当前资金池大小", "yourPoolShare": "你的资金池份额", "noZero": "金额不能为零。", "mustBeETH": "输入中必须有一个是 ETH。", "enterCurrencyOrLabelCont": "输入 {{ inputCurrency }} 或 {{ label }} 值并继续。", "youAreAdding": "你将添加", "and": "和", "intoPool": "入流动资金池。", "outPool": "出流动资金池。", "youWillMint": "你将铸造", "liquidityTokens": "流动通证。", "totalSupplyIs": "当前流动通证的总量是", "youAreSettingExRate": "你将初始兑换率设置为", "totalSupplyIs0": "当前流动通证的总量是0。", "tokenWorth": "当前兑换率下，每个资金池通证价值", "firstLiquidity": "你是第一个添加流动金的人！", "initialExchangeRate": "初始兑换率将由你的存入情况决定。请确保你存入的 ETH 和 {{ label }} 具有相同的总市值。", "removeLiquidity": "删除流动金", "poolTokens": "资金池通证", "enterLabelCont": "输入 {{ label }} 值并继续。", "youAreRemoving": "你正在移除", "youWillRemove": "你将移除", "createExchange": "创建交易所", "invalidTokenAddress": "通证地址无效", "exchangeExists": "{{ label }} 交易所已存在！", "invalidSymbol": "通证符号无效", "invalidDecimals": "小数位数无效", "tokenAddress": "通证地址", "label": "通证符号", "decimals": "小数位数", "enterTokenCont": "输入通证地址并继续"}